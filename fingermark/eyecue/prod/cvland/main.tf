# ==========================================
# IAM Assume Role
# ==========================================

module "iam_password_policy" {
  source = "../../../../modules/iam_password_policy"
}

module "assume_role" {
  source = "../../../../modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "PowerAccess", "DevAccess", "DeployerAccess", "KommisjonAccess", "KommisjonPortForwardAccess"]
}

# ==========================================
# VPC
# ==========================================

module "eyecue_network" {
  source                 = "../../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.customer}_${var.product}_${var.env}_${var.AWS_REGION}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags, { Stack = "network" })
}

# ==========================================
# INFRA Resources
# ==========================================

# TODO: Move these resources to INFRA account

module "ansible_agent" {
  source         = "../../../../modules/ansible-agent"
  aws_account_id = data.aws_caller_identity.current.account_id
}

resource "aws_iam_role" "infra-dynamodbs3-export" {
  name = "infra-dynamodbs3-export-role"

  assume_role_policy = file("${path.module}/data/infra-dynamodbs3-export-permissions-trustpolicy.json")
  tags = {
    Terraform = "true"
  }
}

resource "aws_iam_role" "infra-downtime-scheduler" {
  name = "infra-downtime-scheduler-role"

  assume_role_policy = file("${path.module}/data/infra-downtime-scheduler.json")
  tags = {
    Terraform = "true"
  }
}

resource "aws_iam_policy" "infra-downtime-scheduler" {
  name = "infra-downtime-scheduler-policy"
  policy = templatefile(
    "data/infra-downtime-scheduler-permissions.tpl",
    { DYNAMODB_TABLE_ARN = "arn:aws:dynamodb:ap-southeast-2:************:table/eyecue-dashboard-stores" }
  )
}

resource "aws_iam_role_policy_attachment" "infra-downtime-scheduler_role_policy_attachment" {
  policy_arn = aws_iam_policy.infra-downtime-scheduler.arn
  role       = aws_iam_role.infra-downtime-scheduler.name
}

# ------ Kommisjon ------

module "eyecue_kommisjon" {
  source                         = "../../../../modules/eyecue_kommisjon"
  environment                    = var.ENVIRONMENT
  aws_region                     = var.AWS_REGION
  aws_account_id                 = data.aws_caller_identity.current.account_id
  api_id                         = "agufx41xx5" # Deployed via Kommisjon Serverless
  provisioning_events_table_name = "platform-provisioning-events"
  s3_inventory_bucket_name       = "kommisjon-global-prod-node-inventory"
}

module "platform_provisioning_events" {
  source     = "../../../../modules/provisioning_events"
  table_name = "platform-provisioning-events"
}

# ==========================================
# Computer Vision Development
# ==========================================

module "eyecue_weights_triton" {
  source         = "../../../../modules/eyecue_weights_triton"
  aws_iam_user   = "eyecue-weights-triton"
  bucket_name    = "eyecue-weights"
  aws_region     = var.AWS_REGION
  client_name    = var.CLIENT_NAME
  country        = var.COUNTRY
  aws_account_id = data.aws_caller_identity.current.account_id
}

module "eyecue_dvc_storage" {
  source          = "../../../../modules/eyecue_dvc_storage"
  bucket_name     = "eyecue-training-pipeline"
  iam_user_name   = "dvc-eyecue-training-pipeline"
  secret_name     = "dvc-eyecue-training-pipeline-credentials"
  keybase_pgp_key = var.KEYBASE
  tags = merge(var.default_tags, {
    Stack = "mlops",
    Squad = "Platform"
  })
}

module "backups" {
  source              = "../../../../modules/backups"
  data_lifecycle_name = "CVProdEBSBackups"
}

module "s3_dynamodb_exports" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "3.14.0"
  bucket  = "cv-prod-dynamodb-exports"
  acl     = "private"
  tags    = var.tags
}

# ==========================================
# Auth 0
# ==========================================

module "auth0_migration_user" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 3.0"
  name                          = "auth0-migration"
  create_iam_access_key         = true
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.KEYBASE
  tags = {
    "Terraform"  = true
    "serverless" = false
  }
}

data "aws_iam_policy_document" "auth0_user_policy_document" {
  statement {
    sid = "Auth0CognitoPolicy"
    actions = [
      "cognito-identity:*"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_policy" "auth0_user_policy" {
  name        = "Auth0CognitoPolicy"
  depends_on  = [module.auth0_migration_user]
  description = "Grants read only access to Cloudwatch"
  policy      = data.aws_iam_policy_document.auth0_user_policy_document.json
}

resource "aws_iam_policy_attachment" "auth0_user_policy_attachment" {
  name       = "Auth0CognitoPolicyAttachment"
  users      = ["auth0-migration"]
  policy_arn = aws_iam_policy.auth0_user_policy.arn
}

# ==========================================
# Helm Deployment
# ==========================================

module "helm_repo_iam" {
  source                 = "../../../../modules/s3_bucket_iam"
  aws_iam_user           = "helm-deployer"
  keybase                = var.KEYBASE
  iam_policy_prefix_name = "EyecueHelmPackageSyncIAM"
  bucket_resources_arn_list = [
    "${module.helm_repo.bucket_arn}",
    "${module.helm_repo.bucket_arn}/*",
    "${module.helm_repo_us_east_1.bucket_arn}",
    "${module.helm_repo_us_east_1.bucket_arn}/*"
  ]
  tags = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cicd"
    Product     = "Eyecue"
  }
}

module "helm_repo" {
  enable_versioning = true
  source            = "../../../../modules/s3_bucket"
  bucket_name       = "eyecue-helm-cv-prod-package"
  tags = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cicd"
    Product     = "Eyecue"
  }
}

module "helm_repo_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }
  enable_versioning = true
  source            = "../../../../modules/s3_bucket"
  bucket_name       = "eyecue-helm-cv-prod-us-package"
  tags = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cicd"
    Product     = "Eyecue"
  }
}

# ==========================================
# EYECUE Dashboard
# ==========================================

# ======== IAM ========

resource "aws_iam_role" "dashboard_deployer" {
  name = "Dashboard-Deployer-Permissions"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF

  tags = merge(var.default_tags, { Application = "Heimdallr", Squad = "Eyecue", Serverless = "False" })

}

resource "aws_iam_role_policy" "dashboard_deployer" {
  name = "Dashboard-Deployer-Permissions-policy"
  role = aws_iam_role.dashboard_deployer.id

  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "IoTPerms",
            "Effect": "Allow",
            "Action": [
                "iot:Receive",
                "iot:Subscribe",
                "iot:Connect",
                "iot:GetThingShadow"
            ],
            "Resource": "*"
        },
        {
            "Sid": "dashboarddeployer",
            "Effect": "Allow",
            "Action": [
                "cognito-idp:AdminDeleteUser",
                "s3:GetObject",
                "dynamodb:PutItem",
                "lambda:InvokeFunction",
                "cognito-idp:AdminCreateUser",
                "dynamodb:GetItem",
                "s3:ListBucket",
                "cognito-idp:AdminSetUserPassword",
                "lambda:InvokeAsync",
                "cognito-idp:ListUsers"
            ],
            "Resource": [
                "arn:aws:s3:::ratatoskr-dashboard-templates-${var.env}/*",
                "arn:aws:s3:::ratatoskr-dashboard-templates-${var.env}",
                "arn:aws:cognito-idp:ap-southeast-2:************:userpool/ap-southeast-2_gXD1Y2woQ",
                "arn:aws:dynamodb:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:table/eyecue-deployment-params-template",
                "arn:aws:dynamodb:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:table/eyecue-weights-template",
                "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.env}-ratatoskr-configuration-${var.env}-${var.env}Data",
                "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.env}-ratatoskr-configuration-${var.env}-getImageBySN",
                "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.env}-ratatoskr-configuration-${var.env}-getChromeDevices",
                "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.env}-ratatoskr-configuration-${var.env}-getLogLinks",
                "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:sls-roi-configuration-tool-${var.env}-getClient",
                "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:sls-roi-configuration-tool-${var.env}-getProvisioningDevices",
                "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:sls-roi-configuration-tool-${var.env}-getProvisionedDevices",
                "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:eyecue-provisioning-initiate-provisioning",
                "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:eyecue-provisioning-get-logs",
                "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.env}-ratatoskr-configuration-${var.env}-heimdallrFunction",
                "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${var.env}-ratatoskr-configuration-prod-heimdallrFunction",
                "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:eyecue-provisioning-auto-provisioning-api-vpc-proxy",
                "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:eyecue-provisioning-connect-to-ssh",
                "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:sls-roi-configuration-tool-${var.env}-getAvailableNewSites",
                "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:sls-roi-configuration-tool-${var.env}-addANewSiteToPostgres",
                "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:eyecue-provisioning-get-advance-logs"
            ]
        }
    ]
}
EOF
}

# ======== Domain Names ========

# -------- API Gateway --------

module "heartbeat_api_gateway_domain_cloudflare" {
  source             = "../../../../modules/api_gateway_domain_cloudflare"
  domain_name        = "api.eyecue-heartbeat-service.eyecue.fingermarkai.tech"
  cloudflare_zone_id = "e8428c969a5cc8497a9f8684baba52de"
  endpoint_type      = "EDGE"
  tags               = var.tags
  providers = {
    aws           = aws
    aws.us_east_1 = aws.us-east-1 # Must use us-east-1 for Edge-optimized API Gateway
  }
}

module "eyecue_api_gateway_domain_cloudflare" {
  source             = "../../../../modules/api_gateway_domain_cloudflare"
  domain_name        = "api.eyecuedataboard.com"
  cloudflare_zone_id = "aa00a0cdc808160d406c732989eddbca"
  endpoint_type      = "REGIONAL"
  tags               = var.tags

  providers = { aws.us_east_1 = aws, aws = aws } # Hack around non optional provider
}

module "heimdallr_api_gateway_domain_cloudflare" {
  source             = "../../../../modules/api_gateway_domain_cloudflare"
  domain_name        = "prod.eds.invoke-heimdallr.fingermarkai.tech"
  cloudflare_zone_id = "e8428c969a5cc8497a9f8684baba52de"
  endpoint_type      = "REGIONAL"
  tags               = var.tags

  providers = { aws.us_east_1 = aws, aws = aws } # Hack around non optional provider
}

# -------- Cloudflare DNS --------

module "cloudflare_dns_record_api-eyecuedataboard-com" {
  source                  = "../../../../modules/cloudflare"
  cloudflare_zone_id      = "aa00a0cdc808160d406c732989eddbca"
  cloudflare_record_name  = "api"
  cloudflare_record_value = "d-fukjnlyplk.execute-api.ap-southeast-2.amazonaws.com"
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "CNAME"
}

module "cloudflare_dns_record_beta-api-eyecuedataboard-com" {
  source                  = "../../../../modules/cloudflare"
  cloudflare_zone_id      = "aa00a0cdc808160d406c732989eddbca"
  cloudflare_record_name  = "beta.api"
  cloudflare_record_value = "d-bko2nm82y8.execute-api.ap-southeast-2.amazonaws.com"
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "CNAME"
}

module "cloudflare_dns_record_api-cjs-eyecue-fingermarkai-tech" {
  source                  = "../../../../modules/cloudflare"
  cloudflare_zone_id      = "e8428c969a5cc8497a9f8684baba52de"
  cloudflare_record_name  = "api.cjs.eyecue"
  cloudflare_record_value = "d20rgp3f3kaqkg.cloudfront.net"
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "CNAME"
}

# ======== DynamoDB Tables ========

module "eyecue_dashboard_dynamodb_tables" {
  source                 = "../../../../modules/eyecue_dashboard_dynamodb"
  dynamodb_tables_config = var.dynamodb_tables_config
}

# ======== Events ========

module "eyecue_sns_topic" {
  source     = "../../../../modules/eyecue_sns"
  sns_topics = var.sns_topics
}

module "eyecue_event_handler" {
  source         = "../../../../modules/eyecue_event_handler"
  environment    = var.ENVIRONMENT
  aws_region     = var.AWS_REGION
  aws_account_id = data.aws_caller_identity.current.account_id
}

module "eyecue_notification_service" {
  source         = "../../../../modules/eyecue_notification_service"
  environment    = var.ENVIRONMENT
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
}

# -------- Portal Site --------

module "portal_s3_cloudfront" {
  source             = "../../../../modules/s3_cloudfront"
  s3_bucket_name     = "portal-eyecuedashboard"
  domain_name        = "portal.eyecuedashboard.com"
  cloudfront_aliases = ["portal.eyecuedashboard.com"]
  cloudfront_acm_arn = "arn:aws:acm:us-east-1:************:certificate/53b36fda-54e4-48af-be61-e50f52a25e02"
  tags               = merge(var.default_tags, { Stack = "Eyecue" })
  s3_website = {
    index_document = "index.html"
    error_document = "error.html"
  }
  origin_path                 = "/blue"
  error_response_cachettl     = "300"
  error_response_errorcode    = "403"
  error_response_responsecode = "200"
  error_response_pagepath     = "/index.html"
  block_public_acls           = true
  block_public_policy         = true
  ignore_public_acls          = true
  restrict_public_buckets     = true
}

# -------- Admin Site --------

module "admin_s3_cloudfront" {
  source             = "../../../../modules/s3_cloudfront"
  s3_bucket_name     = "admin-eyecuedashboard"
  domain_name        = "admin.eyecuedashboard.com"
  cloudfront_aliases = ["admin.eyecuedashboard.com"]
  cloudfront_acm_arn = "arn:aws:acm:us-east-1:************:certificate/53b36fda-54e4-48af-be61-e50f52a25e02"
  tags               = merge(var.default_tags, { Stack = "Eyecue" })
  s3_website = {
    index_document = "index.html"
    error_document = "error.html"
  }
  error_response_cachettl     = "300"
  error_response_errorcode    = "403"
  error_response_responsecode = "200"
  error_response_pagepath     = "/index.html"
  block_public_acls           = true
  block_public_policy         = true
  ignore_public_acls          = true
  restrict_public_buckets     = true
}

module "admin_s3_cors" {
  source                  = "../../../../modules/s3_cors_configuration"
  bucket_id               = module.admin_s3_cloudfront.s3_bucket_id
  s3_cors_allowed_origins = ["https://portal.eyecuedashboard.com"]
}

# -------- Dashboard Site --------

module "dashboard_s3_cloudfront" {
  source             = "../../../../modules/s3_cloudfront"
  s3_bucket_name     = "dashboard-eyecuedashboard"
  domain_name        = "dashboard.eyecuedashboard.com"
  cloudfront_aliases = ["dashboard.eyecuedashboard.com"]
  cloudfront_acm_arn = "arn:aws:acm:us-east-1:************:certificate/53b36fda-54e4-48af-be61-e50f52a25e02"
  tags               = merge(var.default_tags, { Stack = "Eyecue" })
  s3_website = {
    index_document = "index.html"
    error_document = "error.html"
  }
  error_response_cachettl     = "300"
  error_response_errorcode    = "403"
  error_response_responsecode = "200"
  error_response_pagepath     = "/index.html"
  block_public_acls           = true
  block_public_policy         = true
  ignore_public_acls          = true
  restrict_public_buckets     = true
}

module "dashboard_s3_cors" {
  source                  = "../../../../modules/s3_cors_configuration"
  bucket_id               = module.dashboard_s3_cloudfront.s3_bucket_id
  s3_cors_allowed_origins = ["https://portal.eyecuedashboard.com"]
}

# -------- Monitor Site --------

module "monitor_s3_cloudfront" {
  source             = "../../../../modules/s3_cloudfront"
  s3_bucket_name     = "monitorv2-eyecuedashboard"
  domain_name        = "monitorv2.eyecuedashboard.com"
  cloudfront_aliases = ["monitorv2.eyecuedashboard.com"]
  cloudfront_acm_arn = "arn:aws:acm:us-east-1:************:certificate/53b36fda-54e4-48af-be61-e50f52a25e02"
  tags               = merge(var.default_tags, { Stack = "Eyecue" })
  s3_website = {
    index_document = "index.html"
    error_document = "error.html"
  }
  error_response_cachettl     = "300"
  error_response_errorcode    = "403"
  error_response_responsecode = "200"
  error_response_pagepath     = "/index.html"
  block_public_acls           = true
  block_public_policy         = true
  ignore_public_acls          = true
  restrict_public_buckets     = true
}

module "monitor_s3_cors" {
  source                  = "../../../../modules/s3_cors_configuration"
  bucket_id               = module.monitor_s3_cloudfront.s3_bucket_id
  s3_cors_allowed_origins = ["https://portal.eyecuedashboard.com"]
}

# -------- Blue Site --------

module "bluedeployment_s3_cloudfront" {
  source             = "../../../../modules/s3_cloudfront"
  s3_bucket_name     = "eyecue-dashboard-blue"
  domain_name        = "blue.eyecuedashboard.com"
  cloudfront_aliases = ["blue.eyecuedashboard.com"]
  cloudfront_acm_arn = "arn:aws:acm:us-east-1:************:certificate/ad04da88-c4af-44e4-9d7c-8966d87d6ac6"
  tags               = merge(var.default_tags, { Stack = "Eyecue" })
  s3_website = {
    index_document = "index.html"
    error_document = "error.html"
  }
  error_response_cachettl     = "300"
  error_response_errorcode    = "403"
  error_response_responsecode = "200"
  error_response_pagepath     = "/index.html"
  block_public_acls           = true
  block_public_policy         = true
  ignore_public_acls          = true
  restrict_public_buckets     = true
}

module "bluedeployment_us_s3_cloudfront" {
  providers = {
    aws = aws.us-east-1
  }
  source             = "../../../../modules/s3_cloudfront"
  s3_bucket_name     = "eyecue-dashboard-blue-us"
  domain_name        = "blue-us.eyecuedashboard.com"
  cloudfront_aliases = ["blue-us.eyecuedashboard.com"]
  cloudfront_acm_arn = "arn:aws:acm:us-east-1:************:certificate/53b36fda-54e4-48af-be61-e50f52a25e02"
  tags               = merge(var.default_tags, { Stack = "Eyecue" })
  s3_website = {
    index_document = "index.html"
    error_document = "error.html"
  }
  error_response_cachettl     = "300"
  error_response_errorcode    = "403"
  error_response_responsecode = "200"
  error_response_pagepath     = "/error.html"
  block_public_acls           = true
  block_public_policy         = true
  ignore_public_acls          = false
  restrict_public_buckets     = false
}

# -------- Green Site --------

module "greendeployment_s3_cloudfront" {
  source             = "../../../../modules/s3_cloudfront"
  s3_bucket_name     = "eyecue-dashboard-green"
  domain_name        = "green.eyecuedashboard.com"
  cloudfront_aliases = ["green.eyecuedashboard.com"]
  cloudfront_acm_arn = "arn:aws:acm:us-east-1:************:certificate/50a48da6-a53f-4e44-8630-bd5542a4be7d"
  tags               = merge(var.default_tags, { Stack = "Eyecue" })
  s3_website = {
    index_document = "index.html"
    error_document = "error.html"
  }
  error_response_cachettl     = "300"
  error_response_errorcode    = "403"
  error_response_responsecode = "200"
  error_response_pagepath     = "/index.html"
  ignore_public_acls          = true
  restrict_public_buckets     = true
}

module "greendeployment_us_s3_cloudfront" {
  providers = {
    aws = aws.us-east-1
  }
  source             = "../../../../modules/s3_cloudfront"
  s3_bucket_name     = "eyecue-dashboard-green-us"
  domain_name        = "green-us.eyecuedashboard.com"
  cloudfront_aliases = ["green-us.eyecuedashboard.com"]
  cloudfront_acm_arn = "arn:aws:acm:us-east-1:************:certificate/53b36fda-54e4-48af-be61-e50f52a25e02"
  tags               = merge(var.default_tags, { Stack = "Eyecue" })
  s3_website = {
    index_document = "index.html"
    error_document = "error.html"
  }
  error_response_cachettl     = "300"
  error_response_errorcode    = "403"
  error_response_responsecode = "200"
  error_response_pagepath     = "/error.html"
  block_public_acls           = true
  block_public_policy         = true
  ignore_public_acls          = false
  restrict_public_buckets     = false
}

# ==========================================
# Monitor
# ==========================================

module "proxy_fire_trigger" {
  source              = "../../../../modules/eyecue_trigger_proxy"
  environment         = var.ENVIRONMENT
  aws_account_id      = data.aws_caller_identity.current.account_id
  target_function     = "eyecue-trigger-service-${var.ENVIRONMENT}-fireTrigger"
  target_region       = "ap-southeast-2"
  proxy_function_name = "eyecue-trigger-proxy-fireTrigger"
  providers = {
    aws = aws.us-east-1
  }
}

module "proxy_get_sites" {
  source              = "../../../../modules/eyecue_trigger_proxy"
  environment         = var.ENVIRONMENT
  aws_account_id      = data.aws_caller_identity.current.account_id
  target_function     = "eyecue-trigger-service-${var.ENVIRONMENT}-getSites"
  target_region       = "ap-southeast-2"
  proxy_function_name = "eyecue-trigger-proxy-getSites"
  providers = {
    aws = aws.us-east-1
  }
}

# ==========================================
# SOC2 Security
# ==========================================

# ------- Vanta / Compliance Detection -------

module "vanta" {
  source = "../../../../modules/vanta"
}

# ------- SQS Monitoring -------

module "sqs_monitoring_ap_southeast_2" {
  source               = "../../../../modules/sqs_monitoring"
  lambda_function_name = "sqs-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]
  tags = merge(var.tags, {
    Compliance = "SOC2"
    Purpose    = "SQSMessageAgeMonitoring"
    Squad      = "platform"
  })
  default_tags = var.default_tags
}

module "sqs_monitoring_us_east_1" {
  source               = "../../../../modules/sqs_monitoring"
  lambda_function_name = "sqs-monitoring-remediation-us-east-1"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]
  tags = merge(var.tags, {
    Compliance = "SOC2"
    Purpose    = "SQSMessageAgeMonitoring"
    Squad      = "platform"
  })
  default_tags = var.default_tags
  providers    = { aws = aws.us-east-1 }
}

# ------- S3 Public Access Block -------

resource "aws_s3_account_public_access_block" "block_public_access" {
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# ------ CloudWatch Log Retention Management ------

module "cw_log_retention_ap_southeast_2" {
  source         = "../../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

module "cw_log_retention_us_east_1" {
  source         = "../../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags

  providers = {
    aws = aws.us-east-1
  }
}

# ------- DynamoDB Dynamic Monitoring -------

module "dynamodb_monitoring_ap_southeast_2" {
  source               = "../../../../modules/dynamodb_monitoring"
  lambda_function_name = "dynamodb-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "DynamoDBMonitoring"
    Environment = "Production"
    Squad       = "platform"
  })
  default_tags = var.default_tags
}

module "dynamodb_monitoring_us_east_1" {
  source               = "../../../../modules/dynamodb_monitoring"
  lambda_function_name = "dynamodb-monitoring-remediation-us-east-1"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "DynamoDBMonitoring"
    Environment = "Production"
    Squad       = "platform"
  })
  default_tags = var.default_tags
}

# ==========================================
# CloudWatch Alarms
# ==========================================

module "rds_cw_alarms" {
  source                                 = "../../../../modules/rds_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_rds_cpu_util           = {}
  cw_alarm_config_rds_mem_free           = {}
  cw_alarm_config_rds_disk_queue_depth   = {}
  cw_alarm_config_rds_write_iops         = {}
  cw_alarm_config_rds_read_iops          = {}
  cw_alarm_config_rds_free_storage_space = {}
  tags                                   = var.tags
  default_tags                           = var.default_tags
}

module "ec2_instance_cw_alarms_ap_southeast_2" {
  # providers      = { aws = aws.ap-southeast-2 } # DEFAULT AWS PROVIDER: ap-southeast-2
  source         = "../../../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "dhcp-server"  = { instance_tags = { Name = "dhcp-server" } }
    "DNS Server"   = { instance_tags = { Name = "DNS Server" } }
    "Meshcentral"  = { instance_tags = { Name = "Meshcentral" } }
    "ssh-tunnel"   = { instance_tags = { Name = "ssh-tunnel" } }
    "teleport"     = { instance_tags = { Name = "teleport" } }
    "thanos-store" = { instance_tags = { Name = "thanos-store" } }
    "vpn-server"   = { instance_tags = { Name = "vpn-server" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}

# Lambda global error rate monitoring for SOC2 compliance
module "lambda_error_monitoring_ap_southeast_2" {
  source = "../../../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Production"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}

# Lambda global error rate monitoring for us-east-1 region
module "lambda_error_monitoring_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }
  source = "../../../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Production"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}
