module "iam_password_policy" {
  source = "../../../../modules/iam_password_policy"
}

module "common" {
  source            = "../../../../modules/common"
  aws_account_id    = data.aws_caller_identity.current.account_id
  aws_region        = var.AWS_REGION
  client_name       = var.CLIENT_NAME
  client_acronym    = var.CLIENT_ACRONYM
  country           = var.COUNTRY
  country_full      = var.COUNTRY_FULL
  aws_iam_roles     = ["AdminAccess", "DevAccess", "PowerAccess", "DeployerAccess", "DataScientist", "QuicksightAdminAccess"]
  keybase           = var.KEYBASE
  cloudcraft_access = true
  env               = var.ENVIRONMENT
}

module "eyecue_notification_service" {
  source         = "../../../../modules/eyecue_notification_service"
  environment    = var.ENVIRONMENT
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
}

module "eyecue_sns_topic" {
  source     = "../../../../modules/eyecue_sns"
  sns_topics = var.sns_topics
}

module "eyecue_network" {
  source                 = "../../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.customer}_${var.product}_${var.env}_${var.AWS_REGION}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags)
}

module "eyecue_rds" {
  source                        = "../../../../modules/eyecue_rds"
  create_replica                = false
  rds_apply_changes_immediately = true
  rds_master_instance_class     = var.RDS_MASTER_INSTANCE_CLASS
  rds_replica_instance_class    = "db.t3.large"
  rds_engine_version            = "16.8"
  rds_ca_cert_identifier        = "rds-ca-rsa2048-g1"
  special_password              = false
  product                       = "Eyecue"
  vpc_id                        = module.eyecue_network.vpc_id
  aws_region                    = var.AWS_REGION
  aws_account_id                = data.aws_caller_identity.current.account_id
  vpc_security_group_ids        = [module.eyecue_network.havelock_security_group_id]
  subnet_ids                    = module.eyecue_network.public_subnet_ids
  rds_allocated_storage         = 200
  rds_max_allocated_storage     = 1000
  rds_replica_iops              = 3000
  rds_master_iops               = 12000
  eyecue_rds_cloudflare_api_key = data.vault_generic_secret.cloudflare.data["api_key"]
  eyecue_rds_customer_id        = var.CLIENT_ACRONYM
  create_db_parameter_group     = true
  parameter_group_family        = "postgres16"
  parameter_group_parameters = [ # Explicitly disable forced SSL
    {
      name  = "rds.force_ssl"
      value = "0"
    }
  ]
  allow_major_version_upgrade = true
}

module "secret_manager" {
  source = "../../../../modules/secret_manager"

  eyecue_postgres_lambdas_secret_name = "rds/ssm/eyecue-postgres-lambdas"

  eyecue_dashboard_data_secret_name = "rds/ssm/eyecue-dashboard-data"
}

resource "aws_key_pair" "infra_team" {
  key_name   = "infra-team"
  public_key = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCnPia4SAqns7GnRzhLsx9W1OjSzrIYuNjJnyMv0X+zNRf55qOkCLpQYjecaSfGO6gIsA63iSEKIXJG2ZQJVHb7FdPlj/racf0nU5J58JWo7rqA28ITeMbu71OTLzvr7KcU6AonWQ116WTQGFjd+RFyq0YjSo5Cc/zlDT4soojlt/sV9p/2AnbiwxZ86BiQXvbOOEW2NeY8Ak88SLP2QmHSsrfIWr0+tYSFtHgrXSkP7x3MEs7L0mNPsKm96LA116gmS3PZc5ZlXU5s9tE1cn7VdhZDeigCxq+RVZltGMaL3LTOxybr4X7g5mMY/rGC/zBTGXyOZORiZbK7oEnTM+If"
  tags       = var.default_tags
}

module "icinga2_satellite" {
  source                                         = "../../../../modules/icinga2_satellite"
  icinga2_satellite_vpc_id                       = module.eyecue_network.vpc_id
  icinga2_satellite_ec2_ami_id                   = "ami-003d3d03cfe1b0468"
  icinga2_satellite_ec2_subnet_id                = module.eyecue_network.public_subnet_ids[1]
  icinga2_satellite_ec2_extra_security_group_ids = [module.eyecue_network.havelock_security_group_id]
  icinga2_satellite_ec2_ssh_key_name             = aws_key_pair.infra_team.key_name
  icinga2_satellite_customer_id                  = var.CLIENT_ACRONYM
  icinga2_satellite_cloudflare_api_key           = data.vault_generic_secret.cloudflare.data["api_key"]
}

module "eyecue_mimir" {
  source                   = "../../../../modules/eyecue_mimir"
  mimir_aws_account_id     = "************"
  lambda_role_arn_suffixes = var.lambda_role_arn_suffixes
  policy_description       = "Policy to invoke request-roisuggestor-png lambda from cross accounts."
  AWS_ACCOUNT_ID           = data.aws_caller_identity.current.account_id
  AWS_REGION               = var.AWS_REGION
}

module "eyecue_customer_edw_integration" {
  source                = "../../../../modules/eyecue_customer_edw_integration"
  client_name           = var.CLIENT_NAME
  country               = var.COUNTRY
  bucket_name_reference = "edw-integration"
  keybase               = var.KEYBASE
}

module "kinesis_data_stream" {
  source                         = "../../../../modules/kinesis_data_stream"
  aws_region                     = var.AWS_REGION
  client_name                    = var.CLIENT_NAME
  redshift_aws_account_ids_roles = var.redshift_aws_account_ids_roles
  retention_period               = var.kinesis_data_stream_retention_period
  stream_mode                    = var.kinesis_data_stream_stream_mode
  firehose_roi_bucket_arn        = "arn:aws:s3:::data-dev-rnd-us-east-1"
  current_account_id             = data.aws_caller_identity.current.account_id
}

module "kinesis_common_data_stream" {
  source                         = "../../../../modules/kinesis_data_stream"
  aws_region                     = var.AWS_REGION
  client_name                    = var.CLIENT_NAME
  redshift_aws_account_ids_roles = var.redshift_aws_account_ids_roles
  retention_period               = var.kinesis_data_stream_retention_period
  stream_mode                    = var.kinesis_data_stream_stream_mode
  stream_name_list               = var.stream_name_list
  current_account_id             = data.aws_caller_identity.current.account_id
  create_role                    = false
}

module "kinesis_cfa_data_access" {
  source                     = "../../../../modules/kinesis_data_stream_client_access"
  client_aws_account_details = var.client_aws_account_details
  client_account_id          = "************"
}

# Keep this module while we work on setting up eyecue_iot_kinesis_eventstream_module
module "eyecue_iot_event_kinesis_module" {
  source                        = "../../../../modules/eyecue_iot_event_kinesis"
  client_acronym                = var.CLIENT_ACRONYM
  client_name_kinesis           = "${var.CLIENT_ACRONYM}-${var.COUNTRY_FULL}"
  kinesis_stream_name_roi       = var.kinesis_stream_name_roi
  kinesis_stream_name_hvi       = var.kinesis_stream_name_hvi
  kinesis_stream_name_aggregate = var.kinesis_stream_name_aggregate
  kinesis_stream_name_departure = var.kinesis_stream_name_departure
}

# This module will replace eyecue_iot_event_kinesis_module
module "eyecue_iot_kinesis_eventstream" {
  source                         = "../../../../modules/eyecue_iot_kinesis_eventstream"
  client_acronym                 = var.CLIENT_ACRONYM
  kinesis_iot_topic_rules_config = var.kinesis_iot_topic_rules_config
  fallback_bucket_name           = "fm-data-eyecue-kinesis-failure-us-east-1"
  enable_fallback_to_s3          = true
}

module "kinesis_databricks_iam_role" {
  source                              = "../../../../modules/kinesis_databricks_iam_role"
  client_granted_stream_resource_arns = var.client_granted_stream_resource_arns
  databricks_role_arn_list            = var.databricks_role_arn_list
  kms_key_arn_list                    = ["arn:aws:kms:us-east-1:************:key/649964f7-baf8-40a8-a2e0-f027f2739489"]
  client_account_id                   = "************" #CFA Databricks Account ID
  tags                                = { "Description" = "Databricks Kinesis Dev Role" }
}

module "kinesis_databricks_prod_iam_role" {
  source                              = "../../../../modules/kinesis_databricks_iam_role"
  client_granted_stream_resource_arns = var.client_granted_stream_resource_arns
  databricks_role_arn_list            = var.databricks_role_prod_arn_list
  kms_key_arn_list                    = ["arn:aws:kms:us-east-1:************:key/649964f7-baf8-40a8-a2e0-f027f2739489"]
  client_account_id                   = "************" #CFA Databricks Prod Account ID
  tags                                = { "Description" = "Databricks Kinesis Prod Role" }
}

############################################
#Kinesis to Redshift integration - new kinesis structure :
#TODO:
#   1. Create a new kinesis stream (journey, drivethru-events, indoor-events)
#   2. Migrate data model to new structure in redshift (data-terraform)
#   3. Share the new kinesis stream with the CFA Databricks account
#   4. Cleanup old kinesis streams

#Kinesis Streams

module "kinesis_redshift_journey_data_stream" {
  source                             = "../../../../modules/kinesis_data_stream"
  aws_region                         = var.AWS_REGION
  client_name                        = var.kinesis_client_name
  redshift_aws_account_ids_roles     = var.redshift_aws_account_ids_roles
  retention_period                   = "72"
  stream_mode                        = "ON_DEMAND"
  stream_name_list                   = ["journey"]
  current_account_id                 = data.aws_caller_identity.current.account_id
  create_role                        = true
  is_data_sharing_enabled            = false
  redshift_stream_access_role_name   = "redshift_stream_access_role"
  redshift_stream_access_policy_name = "kinesis_stream_policy"
}

module "kinesis_redshift_drivethru_events_data_stream" {
  source                         = "../../../../modules/kinesis_data_stream"
  aws_region                     = var.AWS_REGION
  client_name                    = var.kinesis_client_name
  redshift_aws_account_ids_roles = var.redshift_aws_account_ids_roles
  retention_period               = "72"
  stream_mode                    = "ON_DEMAND"
  stream_name_list               = ["drivethru-events"]
  current_account_id             = data.aws_caller_identity.current.account_id
  create_role                    = false
  is_data_sharing_enabled        = false
}

module "kinesis_redshift_indoor_events_data_stream" {
  source                         = "../../../../modules/kinesis_data_stream"
  aws_region                     = var.AWS_REGION
  client_name                    = var.kinesis_client_name
  redshift_aws_account_ids_roles = var.redshift_aws_account_ids_roles
  retention_period               = "72"
  stream_mode                    = "ON_DEMAND"
  stream_name_list               = ["indoor-events"]
  current_account_id             = data.aws_caller_identity.current.account_id
  create_role                    = false
  is_data_sharing_enabled        = false
}

module "eyecue_iot_kinesis_rules" {
  source                                             = "../../../../modules/eyecue_iot_kinesis_eventstream"
  client_acronym                                     = var.kinesis_client_name
  kinesis_iot_topic_rules_config                     = local.kinesis_iot_topic_rules_config
  fallback_bucket_name                               = "fm-data-eyecue-kinesis-failure-us-east-1"
  enable_fallback_to_s3                              = true
  iot_kinesis_eventstream_iam_role_name              = "cfa-usa-iot-kinesis-events-role"
  iot_kinesis_eventstream_iam_policy_name            = "cfa-usa-iot-kinesis-event-policy"
  iot_kinesis_cloudwatch_log_group_name              = "cfa_usa_iot_kinesis"
  iot_kinesis_cloudwatch_log_group_iam_policy_name   = "cfa-usa-iot-kinesis-cloudwatch-access-policy"
  iot_kinesis_eventstream_iam_policy_attachment_name = "cfa-usa-iot-kinesis-event-logs-attachment"
  iot_kinesis_fallback_lambda_iam_role_name          = "cfa-usa-iot-kinesis-fallback-lambda-role"
  iot_kinesis_fallback_lambda_iam_policy_name        = "cfa-usa-iot-kinesis-fallback-lambda-policy"
  iot_kinesis_fallback_lambda_function_name          = "cfa-usa-iot-kinesis-fallback-lambda"
}

############################################

# ==========================================
# SOC2 Security
# ==========================================

module "vanta" {
  source = "../../../../modules/vanta"
}

# ------- SQS Monitoring -------

module "sqs_monitoring_us_east_1" {
  source        = "../../../../modules/sqs_monitoring"
  sns_topic_arn = var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]
  tags = merge(var.tags, {
    Compliance = "SOC2"
    Purpose    = "SQSMessageAgeMonitoring"
    Squad      = "platform"
  })
  default_tags = var.default_tags
}

# ------- S3 Public Access Block -------

resource "aws_s3_account_public_access_block" "block_public_access" {
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# ------ CloudWatch Log Retention Management ------

module "cw_log_retention_ap_southeast_2" {
  source         = "../../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags

  providers = {
    aws = aws.ap-southeast-2
  }
}

module "cw_log_retention_us_east_1" {
  source         = "../../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

# ------- DynamoDB Dynamic Monitoring -------

module "dynamodb_monitoring_ap_southeast_2" {
  source               = "../../../../modules/dynamodb_monitoring"
  lambda_function_name = "dynamodb-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "DynamoDBMonitoring"
    Environment = "Production"
    Squad       = "platform"
  })
  default_tags = var.default_tags
}

module "dynamodb_monitoring_us_east_1" {
  source               = "../../../../modules/dynamodb_monitoring"
  lambda_function_name = "dynamodb-monitoring-remediation-us-east-1"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "DynamoDBMonitoring"
    Environment = "Production"
    Squad       = "platform"
  })
  default_tags = var.default_tags
}

# ===============================================
# CloudWatch Alarms
# ===============================================

module "rds_cw_alarms" {
  source                                 = "../../../../modules/rds_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]]
  cw_alarm_config_rds_cpu_util           = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_mem_free           = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_disk_queue_depth   = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_write_iops         = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_read_iops          = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_free_storage_space = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  tags                                   = var.tags
  default_tags                           = var.default_tags
}

module "ec2_instance_cw_alarms_us_east_1" {
  # providers      = { aws = aws.us-east-1 }  # DEFAULT AWS PROVIDER: us-east-1
  source         = "../../../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "icinga2-satellite" = { instance_tags = { Name = "icinga2-satellite" } }
    "wireguard-proxy"   = { instance_tags = { Name = "wireguard-proxy" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}

# Lambda global error rate monitoring for SOC2 compliance
module "lambda_error_monitoring_us_east_1" {
  source = "../../../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Production"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}
