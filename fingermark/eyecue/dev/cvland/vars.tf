###  Destination AWS Account  ###
data "aws_caller_identity" "current" {}

variable "AWS_REGION" {
  default = "ap-southeast-2"
}
variable "ENVIRONMENT" {}

variable "CLIENT_NAME" {
  default = "cvland-dev"
}

variable "CLIENT_ACRONYM" {
  default = ""
}

variable "COUNTRY" {
  default = "au"
}

variable "COUNTRY_FULL" {
  type    = string
  default = "aus"
}

variable "KEYBASE" {
  default = "keybase:fingermark"
}

### RDS Module ###
variable "RDS_MASTER_INSTANCE_CLASS" {
  default = "db.t3.micro"
}



### NETWORK Module ###

variable "vpc_cidr_block" {
  description = "AWS VPC cidr block"
  type        = string
  default     = "**********/16"
}

variable "vpc_name" {
  description = "Name of the VPC"
  default     = ""
  type        = string
}

variable "vpc_tags" {
  description = "Infrastructure - Network VPC Tags"
  type        = map(any)
  default     = {}
}

variable "public_subnets" {
  description = "Infrastructure - Network Public Subnets List"
  type        = list(any)
  default     = ["**********/20", "***********/20", "***********/20"]
}
variable "vpc_azs" {
  description = "Infrastructure - Network Availability Zones"
  type        = list(any)
  default     = ["ap-southeast-2b", "ap-southeast-2c", "ap-southeast-2a"]
}
variable "private_subnets" {
  description = "Infrastructure - Network Private Subnets List"
  type        = list(any)
  default     = ["***********/20", "***********/20", "***********/20"]
}

variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Stack       = "network"
    Product     = "eyecue"
    Environment = "dev"
  }
}

variable "customer" {
  default     = "fingermark"
  description = "Fingermark Customer Name"
  type        = string
}

variable "env" {
  default     = "dev"
  description = "Fingermark Environment"
  type        = string
}

variable "product" {
  default     = "eyecue"
  description = "Fingermark Product"
  type        = string
}

variable "kinesis_stream_name_roi" {
  type    = string
  default = "ds-cvland-dev-eyecue-roi"
}

variable "kinesis_stream_name_hvi" {
  type    = string
  default = "ds-cvland-dev-eyecue-hvi"
}

variable "kinesis_stream_name_aggregate" {
  type    = string
  default = "ds-cvland-dev-eyecue-aggregate"
}

variable "kinesis_stream_name_departure" {
  type    = string
  default = "ds-cvland-dev-eyecue-departure"
}

variable "kinesis_iot_topic_rules_config" {
  description = "Configuration for each IoT topic rule"
  type = map(object({
    name : string
    sql : string
    stream_name : string
    description : string
    enabled : bool
  }))
  default = {
    "roi_eyecue_iot_kinesis_eventstream" = {
      name        = "roi_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT ROI events to Kinesis Data Stream"
      sql         = "SELECT *, regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)') as store_id, 'roi' as event_type, 'cvland-dev-aus' as client_name FROM '/eyeq/roievent/#'"
      stream_name = "ds-cvland-dev-eyecue-eventstream"
      enabled     = true
    },
    "hvi_eyecue_iot_kinesis_eventstream" = {
      name        = "hvi_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT HVI events to Kinesis Data Stream"
      sql         = "SELECT *, regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)') as store_id, 'hvi' as event_type, 'cvland-dev-aus' as client_name FROM '/eyeq/hci/#'"
      stream_name = "ds-cvland-dev-eyecue-eventstream"
      enabled     = true
    },
    "aggregated_eyecue_iot_kinesis_eventstream" = {
      name        = "aggregated_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Aggregated events to Kinesis Data Stream"
      sql         = "SELECT *, site_id as store_id, 'aggregate' as event_type, 'cvland-dev-aus' as client_name FROM '/eyeq/vehicle-aggregated-2-0/#'"
      stream_name = "ds-cvland-dev-eyecue-eventstream"
      enabled     = true
    },
    "departure_eyecue_iot_kinesis_eventstream" = {
      name        = "departure_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Departure events to Kinesis Data Stream"
      sql         = "SELECT *, site_id as store_id, 'departure' as event_type, 'cvland-dev-aus' as client_name FROM '/eyeq/departure/#'"
      stream_name = "ds-cvland-dev-eyecue-eventstream"
      enabled     = true
    },
    "arrival_eyecue_iot_kinesis_eventstream" = {
      name        = "arrival_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Arrival events to Kinesis Data Stream"
      sql         = "SELECT *, arrival.site_id as store_id, 'arrival' as event_type, 'cvland-dev-aus' as client_name FROM '/eyeq/arrival/#'"
      stream_name = "ds-cvland-dev-eyecue-eventstream"
      enabled     = true
    },
    "danger_zone_eyecue_iot_kinesis_eventstream" = {
      name        = "danger_zone_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Danger Zone events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id, 'danger-zone' as event_type, 'cvland-dev-aus' as client_name FROM '/eyeq/danger-zones/+'"
      stream_name = "ds-cvland-dev-eyecue-eventstream"
      enabled     = true
    },
    "rolling_eyecue_iot_kinesis_eventstream" = {
      name        = "rolling_eyecue_iot_kinesis_eventstream"
      description = "Topic Rule for Forwarding IoT Rolling events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as store_id, 'rolling' as event_type, 'cvland-dev-aus' as client_name FROM '/eyeq/rolling/+'"
      stream_name = "ds-cvland-dev-eyecue-eventstream"
      enabled     = true
    },
    "indoor_tracking_eyecue_iot_kinesis_eventstream" = {
      name        = "indoor_tracking_eyecue_iot_kinesis_eventstream"
      description = "(indoor-tracking) Topic Rule for Forwarding IoT Indoor events to Kinesis Data Stream"
      sql         = "SELECT *, topic(4) as camera_id, 'cvland-dev-aus' as client_name, 'indoor-tracking' as product FROM '/eyeq/indoor/+/#'"
      stream_name = "ds-cvland-dev-eyecue-eventstream"
      enabled     = true
    },
  }
}

variable "sns_topics" {
  type = map(object({
    topic_name : string
    display_name : string
    tags : map(string)
    subscriptions : optional(object({
      protocol : string
      endpoint : string
    }), null)
  }))
  description = "List of SNS topics to create"
  default = {
    cloudwatch-alarm-alert-helper-topic : {
      topic_name   = "cloudwatch-alarm-alert-helper-topic"
      display_name = "Send Alert From SNS to eyecue-app-team-alerts Slack Channel"
      tags = {
        "Environment"     = "Development"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueDashboard"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "false"
      }
      subscriptions = {
        protocol = "email"
        endpoint = "<EMAIL>"
      }
    },
  }
}
variable "dynamodb_tables_config" {
  description = "Configuration for each DynamoDB table"
  type = map(object({
    hash_key : string
    range_key : optional(string)
    stream_enabled : bool
    stream_view_type : string
    tags : map(string)
    point_in_time_recovery : bool
    deletion_protection_enabled : bool
    ttl : optional(object({
      attribute_name : string
      enabled : bool
    }), null)
    gsi : optional(map(object({
      hash_key : string
      range_key : optional(string)
    })), {})
  }))
  default = {
    "eyecue-dashboard-dashboards" = {
      hash_key                    = "siteId"
      range_key                   = "id"
      stream_enabled              = true
      stream_view_type            = "NEW_IMAGE"
      deletion_protection_enabled = true
      tags = {
        "Environment"     = "Development"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueDashboard"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "false"
      }
      point_in_time_recovery = true
      ttl = {
        attribute_name = "expireAt"
        enabled        = true
      }
    },
    "eyecue-dashboard-templates" = {
      hash_key                    = "id"
      stream_enabled              = true
      stream_view_type            = "NEW_IMAGE"
      deletion_protection_enabled = true
      tags = {
        "Environment"     = "Development"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueDashboard"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "false"
      }
      point_in_time_recovery = true
      ttl                    = null
    },
    "eyecue-dashboard-devices" = {
      hash_key                    = "siteId"
      range_key                   = "serialNumber"
      stream_enabled              = true
      stream_view_type            = "NEW_IMAGE"
      deletion_protection_enabled = true
      tags = {
        "Environment"     = "Development"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueDashboard"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "true"
      }
      point_in_time_recovery = true
      ttl                    = null
    },
    "eyecue-dashboard-stores" = {
      hash_key                    = "siteId"
      range_key                   = "clientId"
      stream_enabled              = true
      stream_view_type            = "NEW_IMAGE"
      deletion_protection_enabled = true
      tags = {
        "Environment"     = "Development"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueDashboard"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "true"
      }
      point_in_time_recovery = true
      ttl                    = null
      gsi : {
        "clientId-siteId-index" = {
          hash_key  = "clientId"
          range_key = "siteId"
        }
      }
    },
    "eyecue-report-service-report-definitions" = {
      hash_key                    = "siteId"
      range_key                   = "id"
      stream_enabled              = true
      stream_view_type            = "NEW_IMAGE"
      deletion_protection_enabled = true
      tags = {
        "Environment"     = "Development"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueReportService"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "true"
      }
      point_in_time_recovery = true
      ttl                    = null
    },
    "eyecue-report-service-report-schedule" = {
      hash_key                    = "id"
      stream_enabled              = true
      stream_view_type            = "NEW_IMAGE"
      deletion_protection_enabled = true
      tags = {
        "Environment"     = "Development"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueReportService"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "true"
      }
      point_in_time_recovery = true
      ttl                    = null
    },
   "eyecue-report-service-report-templates" = {
      hash_key                    = "id"
      stream_enabled              = true
      stream_view_type            = "NEW_IMAGE"
      deletion_protection_enabled = true
      tags = {
        "Environment"     = "Development"
        "Product"         = "Eyecue"
        "Terraform"       = "true"
        "Serverless"      = "false"
        "Stack"           = "Application"
        "Application"     = "EyecueReportService"
        "Squad"           = "Eyecue Application"
        "Customer Facing" = "true"
      }
      point_in_time_recovery = true
      ttl                    = null
    }
  }
}

# ===============================================
# CloudWatch Alarms
# ===============================================
variable "cw_alarms_sns_topic_arns_region_lookup" {
  description = <<-DOC
    Map of region names to SNS topic ARNs for CloudWatch alarm notifications. The SNS topic ARNs
    should have already been created in a centralised AWS account (infra #************) using the
    module `modules/cw_alarm_notifications_sns_topic`.
  DOC
  type        = map(string)
  default = {
    "ap-southeast-2" = "arn:aws:sns:ap-southeast-2:************:cloudwatch-alarms-org"
    "ca-central-1"   = "arn:aws:sns:ca-central-1:************:cloudwatch-alarms-org"
    "us-east-1"      = "arn:aws:sns:us-east-1:************:cloudwatch-alarms-org"
    "us-west-1"      = "arn:aws:sns:us-west-1:************:cloudwatch-alarms-org"
    "us-west-2"      = "arn:aws:sns:us-west-2:************:cloudwatch-alarms-org"
  }
}
