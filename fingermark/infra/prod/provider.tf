terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.0"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.17.0"
    }
  }
}

# =====
# Providers: AWS
# =====
# DEFAULT AWS PROVIDER: ap-southeast-2
provider "aws" {
  region = "ap-southeast-2"
  assume_role {
    # The role ARN within Account B to AssumeRole into. Created in step 1.
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "infra"
  }
}

provider "aws" {
  region = "ca-central-1"
  alias  = "ca-central-1"
  assume_role {
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "infra-ca-central-1"
  }
}

provider "aws" {
  region = "us-east-1"
  alias  = "us-east-1"
  assume_role {
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "infra-us-east-1"
  }
}

provider "aws" {
  region = "us-west-1"
  alias  = "us-west-1"
  assume_role {
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "infra-us-west-1"
  }
}

provider "aws" {
  region = "us-west-2"
  alias  = "us-west-2"
  assume_role {
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "infra-us-west-2"
  }
}

# =====
# Providers: Vault
# =====
provider "vault" {
  address = "https://central.infra.fingermark.tech/vault"
}
