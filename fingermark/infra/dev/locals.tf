locals {
  # ===============================================
  # Egress VPN Gateway (For customer firewalls)
  # ===============================================
  vpn_dns_name = "egress-vpn-dev.eyecue.fingermark.tech"

  # ===============================================
  # CloudWatch Alarms
  # ===============================================
  elb_cw_alarms = {
    defaults = {
      ap_southeast_2 = {
        nlb_config = {
          "eyecue-whitelist-vpn-dev-lb" = { lb_name = "eyecue-whitelist-vpn-dev-lb", tg_names = ["eyecue-whitelist-vpn-dev-tg"] }
        }
        alb_config = {
          "teleport-infra-dev-alb" = { lb_name = "teleport-infra-dev-alb", tg_names = ["teleport-infra-dev-tg"] }
        }
      }
    }
  }
}
