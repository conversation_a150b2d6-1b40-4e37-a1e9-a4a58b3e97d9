# ==========================================
# IAM Assume Role
# ==========================================

module "iam_password_policy" {
  source = "../../../modules/iam_password_policy"
}

module "assume_role" {
  source = "../../../modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "DevAccess", "PowerAccess", "DeployerAccess"]
}

# ==========================================
# VPC
# ==========================================

module "eyecue_network" {
  source                 = "../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.customer}_${var.product}_${var.env}_${data.aws_region.current.name}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags)
}

module "vpc_flow_logs" {
  source          = "../../../modules/vpc_flow_logs"
  log_destination = module.vpc_flow_logs_s3.s3_bucket_arn
}

module "vpc_flow_logs_s3" {
  source = "../../../modules/vpc_flow_logs_s3"

  bucket_name = "fingermark-vpc-logs-dev"
  tags        = merge(var.default_tags, var.tags)
}

# ==============================================
# Provisioning
# ==============================================

module "ansible_agent" {
  source         = "../../../modules/ansible-agent"
  aws_account_id = data.aws_caller_identity.current.account_id
}

# ==============================================
# Platform Camera Connectivity Alerts
# ==============================================

module "platform_camera_connectivity_alerts" {
  source        = "../../../modules/platform-camera-connectivity-alerts"
  environment   = "dev"
  sns_topic_arn = "arn:aws:sns:ap-southeast-2:************:camera-connectivity-alerts-AlertsTopic-iv2CIgc5sDrf"
}

# ==============================================
# SOC2 Compliance
# ==============================================

# ------- Vanta / Compliance Detection -------

module "vanta" {
  source = "../../../modules/vanta"
}

# ------- CloudWatch Log Retention Management -------

module "cw_log_retention_ap_southeast_2" {
  source         = "../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

module "cw_log_retention_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }
  source         = "../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

# ------- AWS Config Recorder -------

module "aws_config_recorder" {
  source = "../../../modules/aws_config_recorder"

  recorder_name      = "config-recorder"
  enable_recording   = true
  recording_strategy = "INCLUSION_BY_RESOURCE_TYPES"
  include_resource_types = [
    "AWS::IAM::Group",
    "AWS::IAM::Policy",
    "AWS::IAM::Role",
    "AWS::IAM::User",
  ]
  recording_frequency = "CONTINUOUS"

  # Re-use Control Tower deployed S3 Bucket and SNS topic
  s3_bucket_name = "aws-controltower-logs-************-ap-southeast-2"                               # Log Archive account
  s3_key_prefix  = "o-aydhjv9alg"                                                                    # Organization ID
  sns_topic_arn  = "arn:aws:sns:ap-southeast-2:************:aws-controltower-AllConfigNotifications" # Audit account

  tags         = var.tags
  default_tags = var.default_tags
}

# ------- DynamoDB Dynamic Monitoring -------

module "dynamodb_monitoring_ap_southeast_2" {
  source               = "../../../modules/dynamodb_monitoring"
  lambda_function_name = "dynamodb-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "DynamoDBMonitoring"
    Environment = "Production"
    Squad       = "platform"
  })
  default_tags = var.default_tags
}

module "dynamodb_monitoring_us_east_1" {
  source               = "../../../modules/dynamodb_monitoring"
  lambda_function_name = "dynamodb-monitoring-remediation-us-east-1"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "DynamoDBMonitoring"
    Environment = "Production"
    Squad       = "platform"
  })
  default_tags = var.default_tags
}

# ===============================================
# CloudWatch Alarms
# ===============================================

module "ec2_instance_cw_alarms_ap_southeast_2" {
  # providers      = { aws = aws.ap-southeast-2 } # DEFAULT AWS PROVIDER: ap-southeast-2
  source         = "../../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "foreman-test"                = { instance_tags = { Name = "foreman-test" } }
    "network-openvpn"             = { instance_tags = { Name = "network-openvpn" } }
    "openvpnc-conexa"             = { instance_tags = { Name = "openvpnc-conexa" } }
    "fm-dev-bitbucket-runner-1"   = { instance_tags = { Name = "fm-dev-bitbucket-runner-1" } }
    "teleport-infra-dev-instance" = { instance_tags = { Name = "teleport-infra-dev-instance" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}

module "elb_cw_alarms_ap_southeast_2" {
  source                                        = "../../../modules/elb_cw_alarms"
  sns_topic_arns                                = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_nlb_healthy_host_count        = local.elb_cw_alarms.defaults.ap_southeast_2.nlb_config
  cw_alarm_config_nlb_unhealthy_host_count      = local.elb_cw_alarms.defaults.ap_southeast_2.nlb_config
  cw_alarm_config_alb_healthy_host_count        = local.elb_cw_alarms.defaults.ap_southeast_2.alb_config
  cw_alarm_config_alb_unhealthy_host_count      = local.elb_cw_alarms.defaults.ap_southeast_2.alb_config
  cw_alarm_config_alb_target_response_time      = local.elb_cw_alarms.defaults.ap_southeast_2.alb_config
  cw_alarm_config_alb_httpcode_elb_5xx_count    = local.elb_cw_alarms.defaults.ap_southeast_2.alb_config
  cw_alarm_config_alb_httpcode_target_5xx_count = local.elb_cw_alarms.defaults.ap_southeast_2.alb_config
  tags                                          = var.tags
  default_tags                                  = var.default_tags
}

# Lambda global error rate monitoring for SOC2 compliance
module "lambda_error_monitoring_ap_southeast_2" {
  source = "../../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Production"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}

resource "aws_s3_account_public_access_block" "block_public_access" {
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# ===============================================
# Teleport Infrastructure
# ===============================================

# ACM Certificate for Teleport
module "teleport_certificate" {
  source = "../../../modules/aws_acm_certificate_cloudflare"

  domain_name        = "teleport-dev.infra.fingermark.tech"
  cloudflare_zone_id = "8e0e78445380f3394040f1bfaf93b74a" # fingermark.tech zone
  tags = merge(var.default_tags, var.tags, {
    Name        = "teleport-infra-dev-cert"
    Environment = "development"
    Product     = "eyecue"
    Project     = "teleport-infrastructure"
    Squad       = "platform"
  })
}

# Teleport Module
module "teleport" {
  source = "../../../modules/teleport"

  name                = "teleport-infra-dev"
  vpc_id              = module.eyecue_network.vpc_id
  public_subnet_ids   = module.eyecue_network.public_subnet_ids
  private_subnet_ids  = module.eyecue_network.private_subnet_ids
  alb_certificate_arn = module.teleport_certificate.certificate_arn

  # Instance configuration
  instance_type    = "t3.medium"
  root_volume_size = 20
  data_volume_size = 50

  # SSH access configuration (disabled by default, SSM is primary access method)
  enable_ssh_access = false                 # Set to true if SSH backup access is needed
  key_name          = "francium-infra-team" # Only used if enable_ssh_access = true
  ssh_cidr_blocks   = []                    # Add CIDR blocks here if SSH access is enabled

  # Enable SSM for secure access (primary access method)
  enable_ssm = true

  # Ansible automation configuration
  create_ansible_bucket = true
  ansible_bucket_name   = "teleport-infra-dev-ansible-automation"

  # Health check configuration
  health_check_path                = "/web/login"
  health_check_interval            = 30
  health_check_timeout             = 5
  health_check_healthy_threshold   = 2
  health_check_unhealthy_threshold = 3

  tags = merge(var.default_tags, var.tags, {
    Environment = "development"
    Product     = "eyecue"
    Project     = "teleport-infrastructure"
    Squad       = "platform"
  })

  depends_on = [module.teleport_certificate]
}

# ===============================================
# Egress VPN Gateway (For customer firewalls)
# ===============================================

module "enhanced_whitelist_vpn_gateway" {
  source = "../../../modules/enhanced_whitelist_vpn_gateway"

  # Basic project information
  project_name = "eyecue-whitelist-vpn"
  environment  = "dev"
  aws_region   = data.aws_region.current.name

  # Networking configuration
  vpc_cidr_block       = "*********/16"
  azs                  = ["ap-southeast-2a", "ap-southeast-2b", "ap-southeast-2c"]
  public_subnet_cidrs  = ["*********/24", "*********/24", "*********/24"]
  private_subnet_cidrs = ["***********/24", "***********/24", "***********/24"]
  enable_nat_gateway   = true
  single_nat_gateway   = true # Use a single NAT gateway instead of one per AZ to save EIP

  # VPN configuration
  vpn_gateway_ami       = "ami-09a50a142626e358e" # Ubuntu 22.04 LTS for ap-southeast-2
  vpn_instance_type     = "t2.small"
  admin_ssh_cidr_blocks = [] # Secure production setting - using SSM for access
  vpn_client_cidr_block = "**********/16"

  # OpenVPN Community Edition Configuration
  key_s3_bucket               = "eyecue-dev-vpn-keys"
  ca_s3_key                   = "pki/ca.crt"
  dh_s3_key                   = "pki/dh2048.pem"
  client_config_s3_bucket     = "eyecue-dev-vpn-client-configs"
  create_key_bucket           = true
  create_client_config_bucket = true

  # Auto Scaling configuration
  enable_auto_scaling  = true
  min_size             = 2
  max_size             = 5
  desired_capacity     = 2
  scale_up_threshold   = 70
  scale_down_threshold = 30

  # Load Balancing configuration
  enable_load_balancer = true
  nlb_internal         = false
  vpn_port             = 1194

  # Monitoring & security configuration
  enable_enhanced_monitoring = true
  create_cloudwatch_alarms   = true
  alarm_notification_email   = "<EMAIL>"
  enable_ddos_protection     = false # Can be enabled if AWS Shield is available
  enable_ssm_access          = true

  # Domain whitelist - initially configured to allow all traffic
  # The infrastructure is in place for future FQDN filtering when needed
  allowed_fqdns = [
    # Empty list as we're allowing all traffic for now
    # Can be populated with specific domains when more granular control is needed
  ]

  # Use custom DNS endpoint in client configurations instead of load balancer DNS
  custom_vpn_endpoint = local.vpn_dns_name

  tags = {
    Environment = "dev"
    Product     = "eyecue"
    Terraform   = "true"
    Project     = "EyecueWhitelistVPN"
    Squad       = "Platform"
  }
}

# --- Egress VPN Gateway DNS Record ---
module "vpn_dns_record" {
  source = "../../../modules/cloudflare"

  cloudflare_zone_id      = "8e0e78445380f3394040f1bfaf93b74a" # fingermark.tech zone
  cloudflare_record_name  = local.vpn_dns_name
  cloudflare_record_value = module.enhanced_whitelist_vpn_gateway.load_balancer_dns_name
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "CNAME"
}
